import React, { useState, useEffect } from "react";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Send, Tag, Trash2, Edit, RefreshCcw, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import {
	Dialog,
	DialogContent,
	DialogPortal,
	DialogOverlay,
} from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { EditPatientSheet } from "./EditPatientSheet";
import { PatientAppointmentsTab } from "./PatientAppointmentsTab";
import { PatientFormsTab } from "./PatientFormsTab";
import {
	useClientDetail,
	transformClientDetailToPatientData,
	useDeleteClient,
} from "@/hooks/useClients";

import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";

interface PatientDetailsSheetProps {
	open: boolean;
	onClose: () => void;
	clientId?: string | number;
	patient?: {
		name: string;
		status: string;
	};
	onDelete?: () => void;
	initialTab?: string;
}

export const PatientDetailsSheet: React.FC<PatientDetailsSheetProps> = ({
	open,
	onClose,
	clientId,
	patient = { name: "Unknown Patient", status: "Unknown" },
	onDelete,
	initialTab = "details",
}) => {
	const [activeTab, setActiveTab] = useState(initialTab);
	const [newMessage, setNewMessage] = useState("");
	const [isActive, setIsActive] = useState(false);
	const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [isPriorityModalOpen, setIsPriorityModalOpen] = useState(false);
	const [selectedPriority, setSelectedPriority] = useState("regular");
	const [currentPriority, setCurrentPriority] = useState("regular");

	useEffect(() => {
		if (open) {
			setActiveTab(initialTab);
		}
	}, [open, initialTab]);

	const {
		data: clientDetailData,
		isLoading,
		error,
		refetch,
	} = useClientDetail(clientId || "", {
		enabled: !!clientId && open,
	});

	const deleteClientMutation = useDeleteClient({
		onSuccess: () => {
			setIsDeleteDialogOpen(false);
			onClose();
			onDelete?.();
		},
		onError: (error) => {
			console.error("Failed to delete client:", error);
		},
	});

	useEffect(() => {
		if (clientDetailData?.data?.is_active !== undefined) {
			setIsActive(clientDetailData.data.is_active);
		}
		if (clientDetailData?.data?.priority) {
			console.log(
				"Priority from backend:",
				clientDetailData.data.priority
			);
			setCurrentPriority(clientDetailData.data.priority);
		}
	}, [clientDetailData]);

	const handleEdit = () => {
		setIsEditSheetOpen(true);
	};

	const handleEditSheetClose = () => {
		setIsEditSheetOpen(false);
	};

	const handleEditSubmit = (data: any) => {
		console.log("Updated patient data:", data);
		setIsEditSheetOpen(false);
	};

	const handleDelete = () => {
		setIsDeleteDialogOpen(true);
	};

	const handleConfirmDelete = () => {
		if (clientId) {
			deleteClientMutation.mutate(clientId);
		}
	};

	const handleCancelDelete = () => {
		setIsDeleteDialogOpen(false);
	};

	const handleSendMessage = () => {
		if (newMessage.trim()) {
			console.log("Sending message:", newMessage);
			setNewMessage("");
		}
	};

	const handlePriorityClick = () => {
		const priorityToUse = patientData.priority || currentPriority;
		setSelectedPriority(priorityToUse);
		setIsPriorityModalOpen(true);
	};

	const handlePriorityModalClose = () => {
		setIsPriorityModalOpen(false);
	};

	const handlePrioritySave = () => {
		console.log("Saving priority:", selectedPriority);
		setCurrentPriority(selectedPriority);
		setIsPriorityModalOpen(false);
	};

	const getPriorityConfig = (priority: string) => {
		switch (priority) {
			case "high":
				return {
					label: "High",
					bgColor: "bg-[#FEE2E2]",
					textColor: "text-[#DC2626]",
					hoverColor: "hover:bg-[#FED7D7]",
				};
			case "medium":
				return {
					label: "Medium",
					bgColor: "bg-[#FEF3C7]",
					textColor: "text-[#D97706]",
					hoverColor: "hover:bg-[#FDE68A]",
				};
			case "regular":
			default:
				return {
					label: "Regular",
					bgColor: "bg-[#E5E7EB]",
					textColor: "text-[#6B7280]",
					hoverColor: "hover:bg-[#D1D5DB]",
				};
		}
	};

	const patientData = clientDetailData
		? transformClientDetailToPatientData(clientDetailData)
		: {
				name: patient.name,
				priority: "regular",
				status: patient.status,
				email: "",
				phone: "",
				patientId: "",
				categories: [],
				customIntake: [],
				forms: [],
				messages: [],
			};

	const getInitials = (name: string) => {
		return name
			.split(" ")
			.map((n) => n[0])
			.join("")
			.toUpperCase();
	};

	const tabItems = [
		{ value: "details", label: "Details" },
		{ value: "forms", label: "Forms" },
		{ value: "message", label: "Message" },
		{ value: "appointments", label: "Appointments" },
	];

	if (isLoading && clientId) {
		return (
			<Sheet open={open} onOpenChange={onClose}>
				<SheetContent className="w-full !max-w-[525px] overflow-y-auto p-6 [&>button]:hidden">
					<div className="flex min-h-[400px] items-center justify-center">
						<div className="text-center">
							<RefreshCcw className="mx-auto h-8 w-8 animate-spin text-gray-400" />
							<p className="mt-2 text-sm text-gray-500">
								Loading client details...
							</p>
						</div>
					</div>
				</SheetContent>
			</Sheet>
		);
	}

	if (error && clientId) {
		return (
			<Sheet open={open} onOpenChange={onClose}>
				<SheetContent className="w-full !max-w-[525px] overflow-y-auto p-6 [&>button]:hidden">
					<div className="flex min-h-[400px] items-center justify-center">
						<div className="text-center">
							<p className="text-sm text-red-600">
								Failed to load client details
							</p>
							<Button
								variant="outline"
								onClick={() => refetch()}
								className="mt-2"
							>
								Try Again
							</Button>
						</div>
					</div>
				</SheetContent>
			</Sheet>
		);
	}
	return (
		<>
			<Sheet open={open} onOpenChange={onClose}>
				<SheetContent className="h-screen w-full !max-w-[525px] p-0 [&>button]:hidden">
					<div className="flex h-full flex-col">
						<div
							className={`flex-1 overflow-y-auto ${activeTab === "message" ? "flex flex-col" : ""} p-6`}
						>
							<div
								className={`flex flex-col items-start justify-start ${activeTab === "message" ? "h-full" : "gap-4"}`}
							>
								<div className="inline-flex w-full items-center justify-start gap-4">
									<div className="flex flex-1 items-center justify-start gap-2">
										<Avatar className="h-7 w-7 rounded-full">
											{clientDetailData?.data
												?.profile_picture_url && (
												<AvatarImage
													src={
														clientDetailData.data
															.profile_picture_url
													}
													alt={patientData.name}
												/>
											)}
											<AvatarFallback className="text-base font-semibold">
												{getInitials(patientData.name)}
											</AvatarFallback>
										</Avatar>
										<div className="text-lg font-semibold">
											{patientData.name}
										</div>
										<div
											className={`flex cursor-pointer items-center gap-1 rounded-md px-2 py-1 transition-colors ${getPriorityConfig(patientData.priority || currentPriority).bgColor} ${getPriorityConfig(patientData.priority || currentPriority).textColor} ${getPriorityConfig(patientData.priority || currentPriority).hoverColor}`}
											onClick={handlePriorityClick}
										>
											<Tag className="h-3 w-3" />
											<span className="text-[10px] font-medium">
												{
													getPriorityConfig(
														patientData.priority ||
															currentPriority
													).label
												}
											</span>
										</div>
									</div>
									<div className="flex items-center justify-start gap-2">
										<span className="text-xs font-medium">
											Active
										</span>
										<div className="flex items-center gap-1.5">
											<Switch
												checked={isActive}
												onCheckedChange={setIsActive}
											/>
											<span className="text-xs text-gray-500">
												{isActive ? "On" : "Off"}
											</span>
										</div>
									</div>
								</div>
								<div className="flex w-full flex-col">
									<div className="border-t border-gray-200 py-3">
										<div className="text-[10px] text-gray-500">
											Email Address
										</div>
										<div className="text-sm">
											{patientData.email}
										</div>
									</div>
									<div className="flex w-full">
										<div className="flex-1 border-t border-gray-200 py-3">
											<div className="text-[10px] text-gray-500">
												Phone Number
											</div>
											<div className="text-sm">
												{patientData.phone}
											</div>
										</div>
										<div className="flex-1 border-t border-gray-200 py-3">
											<div className="text-[10px] text-gray-500">
												Patient ID
											</div>
											<div className="text-sm">
												{patientData.patientId}
											</div>
										</div>
									</div>
									<div className="border-t border-gray-200 py-3">
										<div className="text-[10px] text-gray-500">
											Category
										</div>
										<div className="overflow-x-scroll [&::-webkit-scrollbar]:hidden">
											<div className="flex gap-[5px]">
												{patientData.categories.map(
													(
														category: string,
														index: number
													) => {
														const categoryColors = [
															"bg-blue-100 text-blue-800",
															"bg-green-100 text-green-800",
															"bg-purple-100 text-purple-800",
															"bg-yellow-100 text-yellow-800",
															"bg-pink-100 text-pink-800",
															"bg-indigo-100 text-indigo-800",
														];
														const colorClass =
															categoryColors[
																index %
																	categoryColors.length
															];

														return (
															<div
																key={index}
																className={`h-[22px] rounded-md px-2 py-1 text-nowrap ${colorClass}`}
															>
																<div className="text-[10px] font-medium">
																	{category}
																</div>
															</div>
														);
													}
												)}
											</div>
										</div>
									</div>
								</div>
								<div className="flex h-10 w-full rounded-lg bg-gray-100 p-1">
									{tabItems.map((tab) => (
										<div
											key={tab.value}
											onClick={() =>
												setActiveTab(tab.value)
											}
											className={`flex h-8 flex-1 cursor-pointer items-center justify-center px-3 py-1 ${
												tab.value === activeTab
													? "rounded-md bg-white shadow-sm"
													: ""
											}`}
										>
											<div
												className={`text-xs font-medium ${
													tab.value === activeTab
														? "text-gray-900"
														: "text-gray-500"
												}`}
											>
												{tab.label}
											</div>
										</div>
									))}
								</div>
								{activeTab === "details" && (
									<div className="flex w-full flex-col">
										{patientData.customIntake.map(
											(item: any, index: number) => (
												<div
													key={index}
													className="border-t border-gray-200 py-3"
												>
													<div className="text-[10px] text-gray-500">
														{item.label}
													</div>
													<div className="text-sm">
														{item.value}
													</div>
												</div>
											)
										)}
									</div>
								)}

								{activeTab === "forms" && (
									<PatientFormsTab clientId={clientId} />
								)}

								{activeTab === "message" && (
									<div className="flex w-full flex-1 flex-col rounded-lg bg-[#F8F9FA] p-2">
										<div className="flex h-full flex-col">
											<div className="flex flex-1 flex-col justify-end overflow-y-auto">
												<div className="flex flex-col gap-3">
													<div className="flex items-center justify-center gap-4">
														<div className="h-0 flex-1 border-t border-[#E5E7EB]"></div>
														<div className="text-center text-xs font-medium text-[#9CA3AF]">
															Today
														</div>
														<div className="h-0 flex-1 border-t border-[#E5E7EB]"></div>
													</div>

													<div className="flex items-start justify-start gap-2">
														<div className="flex h-9 w-9 items-center justify-center rounded-full bg-[#E5E7EB]">
															<div className="text-center text-xs font-medium text-[#9CA3AF]">
																AB
															</div>
														</div>
														<div className="flex w-72 flex-col items-start justify-start gap-1">
															<div className="overflow-hidden rounded-md bg-[#005893]/25 px-2 py-1.5">
																<div className="text-xs text-[#111827]">
																	Hello!
																</div>
															</div>
															<div className="flex items-center justify-start gap-1">
																<div className="text-[8px] text-[#111827]">
																	12:55 am
																</div>
															</div>
														</div>
													</div>

													{/*	<div className="flex items-start justify-end gap-2">
														<div className="flex w-72 flex-col items-end justify-start gap-1">
															<div className="w-64 overflow-hidden rounded-md bg-[#E5E7EB] px-2 py-1.5">
																<div className="text-xs text-[#111827]">
																	I want to
																	confrim that
																	your
																	appointment
																	is still
																	holding
																	today
																</div>
															</div>
															<div className="flex items-center justify-end gap-1">
																<Check className="h-3.5 w-3.5 text-[#005893]" />
																<div className="text-[8px] text-[#111827]">
																	12:55 am
																</div>
															</div>
														</div>
														<div className="flex h-9 w-9 items-center justify-center rounded-full bg-[#E5E7EB]">
															<div className="text-center text-xs font-medium text-[#9CA3AF]">
																AB
															</div>
														</div>
													</div>

													<div className="flex items-start justify-end gap-2">
														<div className="flex w-72 flex-col items-end justify-start gap-1">
															<div className="overflow-hidden rounded-md bg-[#E5E7EB] px-2 py-1.5">
																<div className="text-xs text-[#111827]">
																	Hey
																</div>
															</div>
															<div className="flex items-center justify-end gap-1">
																<Check className="h-3.5 w-3.5 text-[#005893]" />
																<div className="text-[8px] text-[#111827]">
																	12:55 am
																</div>
															</div>
														</div>
														<div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#E5E7EB]">
															<div className="text-center text-xs font-medium text-[#9CA3AF]">
																AB
															</div>
														</div>
													</div> */}
												</div>
											</div>

											<div className="flex flex-col items-start justify-end gap-2.5 py-2">
												<div className="flex w-full items-start justify-start gap-2">
													<Input
														className="h-9 flex-1 rounded-md border border-[#D1D5DB] bg-white px-3 py-2 text-xs text-[#9CA3AF] placeholder:text-[#9CA3AF]"
														placeholder="Enter message"
														value={newMessage}
														onChange={(e) =>
															setNewMessage(
																e.target.value
															)
														}
														onKeyDown={(e) => {
															if (
																e.key ===
																"Enter"
															)
																handleSendMessage();
														}}
													/>
													<Button
														className="h-9 w-9 rounded-md bg-[#005893] p-0 hover:bg-[#004a7a]"
														onClick={
															handleSendMessage
														}
													>
														<Send className="h-3 w-3 text-white" />
													</Button>
												</div>
											</div>
										</div>
									</div>
								)}

								{activeTab === "appointments" && (
									<PatientAppointmentsTab />
								)}
							</div>
						</div>
						<div className="bg-white p-6">
							<div className="flex w-full items-center justify-between gap-2.5">
								<Button
									variant="outline"
									size="icon"
									className="h-9 w-9 border-[#DC2626]"
									onClick={handleDelete}
								>
									<Trash2 className="h-3.5 w-3.5 text-[#DC2626]" />
								</Button>
								<div className="flex flex-1 items-center justify-end gap-3">
									<Button
										variant="outline"
										onClick={onClose}
										className="h-9"
									>
										Close
									</Button>
									<Button
										onClick={handleEdit}
										className="h-9"
									>
										<Edit />
										Edit
									</Button>
								</div>
							</div>
						</div>
					</div>
				</SheetContent>
				<EditPatientSheet
					open={isEditSheetOpen}
					onOpenChange={handleEditSheetClose}
					onSubmit={handleEditSubmit}
					clientId={clientId?.toString()}
				/>
			</Sheet>
			<DeleteConfirmationDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
				title="Are you sure you want to delete this patient?"
				description={`This action cannot be undone and will permanently delete ${patientData?.name || "this patient"} and all their information.`}
				onConfirm={handleConfirmDelete}
				onCancel={handleCancelDelete}
				confirmText="Delete Patient"
				isLoading={deleteClientMutation.isPending}
			/>

			<Dialog
				open={isPriorityModalOpen}
				onOpenChange={handlePriorityModalClose}
			>
				<DialogPortal>
					<DialogOverlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[1006] bg-black/20" />
					<DialogContent
						className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-[1007] w-96 max-w-[384px] translate-x-[-50%] translate-y-[-50%] rounded-lg border bg-white p-6 shadow-lg duration-200"
						showCloseButton={false}
					>
						<div className="flex flex-col items-end justify-start gap-4">
							<div className="flex flex-col items-start justify-start gap-1.5 self-stretch">
								<div className="justify-start self-stretch text-base leading-none font-semibold text-gray-900">
									Set Priority
								</div>
								<div className="justify-start self-stretch text-xs leading-none font-normal text-gray-600">
									Determine the priority of {patientData.name}
								</div>
							</div>
							<RadioGroup
								value={selectedPriority}
								onValueChange={setSelectedPriority}
								className="flex flex-col gap-4 self-stretch"
							>
								<div className="inline-flex items-start justify-start gap-2 self-stretch">
									<RadioGroupItem
										value="high"
										id="high"
										className="relative h-3 w-3 rounded-full border border-blue-600"
									/>
									<label
										htmlFor="high"
										className="inline-flex cursor-pointer flex-col items-start justify-center gap-1.5"
									>
										<div className="justify-center self-stretch text-xs leading-3 font-medium text-gray-900">
											High Priority
										</div>
									</label>
								</div>

								<div className="inline-flex items-start justify-start gap-2 self-stretch">
									<RadioGroupItem
										value="medium"
										id="medium"
										className="relative h-3 w-3 rounded-full border border-blue-600"
									/>
									<label
										htmlFor="medium"
										className="inline-flex cursor-pointer flex-col items-start justify-center gap-1.5"
									>
										<div className="justify-center self-stretch text-xs leading-3 font-medium text-gray-900">
											Medium Priority
										</div>
									</label>
								</div>

								<div className="inline-flex items-start justify-start gap-2 self-stretch">
									<RadioGroupItem
										value="regular"
										id="regular"
										className="relative h-3 w-3 rounded-full border border-blue-600"
									/>
									<label
										htmlFor="regular"
										className="inline-flex cursor-pointer flex-col items-start justify-center gap-1.5"
									>
										<div className="justify-center self-stretch text-xs leading-3 font-medium text-gray-900">
											Regular Priority
										</div>
									</label>
								</div>
							</RadioGroup>
							<div className="inline-flex items-center justify-end gap-3 self-stretch">
								<Button
									variant="outline"
									onClick={handlePriorityModalClose}
									className="h-9 w-32 min-w-32"
								>
									Cancel
								</Button>
								<Button
									onClick={handlePrioritySave}
									className="h-9 min-w-32"
								>
									Save
								</Button>
							</div>
							<button
								onClick={handlePriorityModalClose}
								className="absolute top-4 right-4 h-4 w-4 rounded-sm opacity-70 transition-opacity hover:opacity-100"
							>
								<X className="h-4 w-4 text-gray-500" />
							</button>
						</div>
					</DialogContent>
				</DialogPortal>
			</Dialog>
		</>
	);
};
